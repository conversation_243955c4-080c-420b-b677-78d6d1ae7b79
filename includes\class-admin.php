<?php
/**
 * Admin Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Admin
{
    private $parent;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;

        // Register AJAX handlers


    }



    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_scripts($hook)
    {
        // Load on Q-Updater settings page and plugins page
        if ($hook != 'settings_page_q-updater' && $hook != 'plugins.php')
            return;

        wp_enqueue_style('q-updater-admin', plugins_url('css/admin.css', Q_UPDATER_PLUGIN_FILE), [], '1.0.1');
        wp_enqueue_script('q-updater-admin', plugins_url('js/admin.js', Q_UPDATER_PLUGIN_FILE), ['jquery'], '1.0.1', true);



        // Add Chart.js for analytics
        if ($hook == 'settings_page_q-updater') {
            // Only load Chart.js on the plugin's admin page
            wp_enqueue_script('qu-chartjs', 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js', [], '4.4.0', true);

            // Add fallback for Chart.js loading
            wp_add_inline_script('qu-chartjs', '
                window.addEventListener("load", function() {
                    if (typeof Chart === "undefined") {
                        console.warn("Chart.js failed to load from CDN, attempting fallback");
                        // You could add a local fallback here if needed
                    }
                });
            ');


        }

        // Get CSRF protection instance
        $csrf_protection = $this->parent->get_csrf_protection();

        // Create nonces with enhanced security
        wp_localize_script('q-updater-admin', 'qUpdater', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => $csrf_protection->create_nonce('q_updater_nonce', 3600), // 1 hour lifetime
            'bulk_update_nonce' => $csrf_protection->create_nonce('bulk_update_q_plugins', 3600), // 1 hour lifetime
            'dismiss_nonce' => $csrf_protection->create_nonce('q_updater_dismiss_notice', 86400), // 24 hour lifetime
            'settings_url' => admin_url('options-general.php?page=q-updater&tab=settings'),
            'review_submit_nonce' => $csrf_protection->create_nonce('qu_submit_review', 1800), // 30 minute lifetime
            'review_delete_nonce' => $csrf_protection->create_nonce('qu_delete_review', 1800), // 30 minute lifetime
            'analytics_nonce' => $csrf_protection->create_nonce('qu_analytics_nonce', 3600), // 1 hour lifetime
            'github_api_debug_nonce' => $csrf_protection->create_nonce('qu_github_api_debug', 3600), // 1 hour lifetime

            'github_debug_url' => admin_url('options-general.php?page=q-updater-github-debug'),
            'csrf_header' => 'X-CSRF-Token', // CSRF header name for AJAX requests
        ));
    }

    /**
     * Show update notices in admin
     */
    public function show_update_notices()
    {
        if (!current_user_can('update_plugins'))
            return;

        $updates = get_site_transient('update_plugins');
        if (empty($updates->response))
            return;

        foreach ($updates->response as $plugin_file => $plugin_data) {
            if (strpos($plugin_file, 'q-') === false)
                continue;

            $plugin_name = dirname($plugin_file);
            printf(
                '<div class="notice notice-warning"><p>' .
                /* translators: 1: plugin name, 2: version number, 3: update URL */
                __('A new version of <strong>%1$s</strong> is available (v%2$s). ', 'q-updater') .
                '<a href="%3$s">' . __('Update now', 'q-updater') . '</a>.' .
                '</p></div>',
                esc_html($plugin_name),
                esc_html($plugin_data->new_version),
                wp_nonce_url(self_admin_url('update.php?action=upgrade-plugin&plugin=' . $plugin_file), 'upgrade-plugin_' . $plugin_file)
            );
        }
    }


}
