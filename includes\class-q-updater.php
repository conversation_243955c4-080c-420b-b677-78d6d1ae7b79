<?php
/**
 * Main Q-Updater Class
 *
 * This is the main plugin class that initializes all components and hooks into WordPress.
 * It serves as the central hub for all plugin functionality and provides access to
 * component instances through getter methods.
 *
 * @package Q-Updater
 * @since 1.0.0
 * <AUTHOR>
 * @link https://github.com/shamielo/q-updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Main Q-Updater class.
 *
 * Coordinates all plugin functionality through component classes and provides
 * centralized access to plugin settings and options.
 *
 * @since 1.0.0
 */
class Q_Updater
{
    /**
     * List of managed plugins
     *
     * @since 1.0.0
     * @var array
     */
    private $plugins = [];

    /**
     * Option name for plugin logs
     *
     * @since 1.0.0
     * @var string
     */
    private $log_option = 'q_updater_log';

    /**
     * Option name for auto-update settings
     *
     * @since 1.0.0
     * @var string
     */
    private $auto_update_option = 'q_updater_auto_update_plugins';

    /**
     * Option name for email notification settings
     *
     * @since 1.0.0
     * @var string
     */
    private $email_notification_option = 'q_updater_email_notifications';

    /**
     * Transient prefix for GitHub releases cache
     *
     * @since 1.0.0
     * @var string
     */
    private $releases_transient_prefix = 'q_updater_releases_';

    /**
     * Option name for GitHub token
     *
     * @since 1.0.0
     * @var string
     */
    private $github_token_option = 'q_updater_github_token';

    /**
     * Nonce name for GitHub plugin installation
     *
     * @since 1.0.0
     * @var string
     */
    private $github_install_nonce = 'q_updater_install_nonce';

    /**
     * Option name for version history
     *
     * @since 1.0.0
     * @var string
     */
    private $version_history_option = 'q_updater_version_history';

    /**
     * Option name for update frequency setting
     *
     * @since 1.0.0
     * @var string
     */
    private $update_frequency_option = 'q_updater_update_frequency';

    /**
     * Option name for dashboard notification setting
     *
     * @since 1.0.0
     * @var string
     */
    private $dashboard_notification_option = 'q_updater_dashboard_notifications';

    /**
     * Option name for custom repository mappings
     *
     * @since 1.0.0
     * @var string
     */
    private $custom_repo_mappings_option = 'q_updater_custom_repo_mappings';

    /**
     * Option name for plugin backups
     *
     * @since 1.0.0
     * @var string
     */
    private $backups_option = 'q_updater_backups';

    /**
     * Option name for developer email setting
     *
     * @since 1.0.0
     * @var string
     */
    private $developer_email_option = 'q_updater_developer_email';

    /**
     * Option name for analytics settings
     *
     * @since 1.0.0
     * @var string
     */
    private $analytics_option = 'q_updater_analytics_settings';



    /**
     * Option name for token sync settings
     *
     * @since 1.1.3
     * @var string
     */
    private $token_sync_is_master_option = 'q_updater_token_sync_is_master';

    /**
     * Option name for token sync master URL
     *
     * @since 1.1.3
     * @var string
     */
    private $token_sync_master_url_option = 'q_updater_token_sync_master_url';

    /**
     * Option name for token sync key
     *
     * @since 1.1.3
     * @var string
     */
    private $token_sync_key_option = 'q_updater_token_sync_key';

    /**
     * Option name for token sync connected sites
     *
     * @since 1.1.3
     * @var string
     */
    private $token_sync_connected_sites_option = 'q_updater_token_sync_connected_sites';

    /**
     * GitHub API component instance
     *
     * @since 1.0.0
     * @var Q_Updater_GitHub_API
     */
    private $github_api;

    /**
     * Plugin Manager component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Plugin_Manager
     */
    private $plugin_manager;

    /**
     * Settings component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Settings
     */
    private $settings;

    /**
     * Updates component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Updates
     */
    private $updates;

    /**
     * Admin component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Admin
     */
    private $admin;

    /**
     * Notifications component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Notifications
     */
    private $notifications;

    /**
     * Auto Updater component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Auto_Updater
     */
    private $auto_updater;

    /**
     * Security component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Security
     */
    private $security;

    /**
     * Reviews component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Reviews
     */
    private $reviews;

    /**
     * Analytics component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Analytics
     */
    private $analytics;

    /**
     * Token Manager component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Token_Manager
     */
    private $token_manager;

    /**
     * CSRF Protection component instance
     *
     * @since 1.0.0
     * @var Q_Updater_CSRF_Protection
     */
    private $csrf_protection;

    /**
     * Encryption component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Encryption
     */
    private $encryption;

    /**
     * Migration component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Migration
     */
    private $migration;

    /**
     * Cron Manager component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Cron_Manager
     */
    private $cron_manager;



    /**
     * Token Sync component instance
     *
     * @since 1.1.3
     * @var Q_Updater_Token_Sync
     */
    private $token_sync;

    /**
     * Token Sync Client component instance
     *
     * @since 1.1.3
     * @var Q_Updater_Token_Sync_Client
     */
    private $token_sync_client;

    /**
     * Token Sync AJAX component instance
     *
     * @since 1.1.3
     * @var Q_Updater_Token_Sync_AJAX
     */
    private $token_sync_ajax;



    /**
     * Constructor
     *
     * Initializes the plugin by loading dependencies, instantiating component classes,
     * and setting up WordPress hooks. The initialization sequence is important:
     * 1. Load all required class files
     * 2. Initialize security component first
     * 3. Initialize all other components
     * 4. Set up WordPress hooks
     * 5. Schedule update checks
     *
     * @since 1.0.0
     */
    public function __construct()
    {
        // Load dependencies
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-security.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-github-api.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-plugin-manager.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-settings.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-updates.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-admin.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-notifications.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-auto-updater.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-reviews.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-analytics.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-token-manager.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-csrf-protection.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-encryption.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-migration.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-cron-manager.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-token-sync.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-token-sync-client.php';
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/class-token-sync-ajax.php';


        // Initialize security first
        $this->security = new Q_Updater_Security($this);

        // Initialize other components
        $this->github_api = new Q_Updater_GitHub_API($this);
        $this->plugin_manager = new Q_Updater_Plugin_Manager($this);
        $this->settings = new Q_Updater_Settings($this);
        $this->updates = new Q_Updater_Updates($this);
        $this->admin = new Q_Updater_Admin($this);
        $this->notifications = new Q_Updater_Notifications($this);
        $this->auto_updater = new Q_Updater_Auto_Updater($this);
        $this->reviews = new Q_Updater_Reviews($this);
        $this->analytics = new Q_Updater_Analytics($this);
        $this->token_manager = new Q_Updater_Token_Manager($this);
        $this->csrf_protection = new Q_Updater_CSRF_Protection($this);
        $this->encryption = new Q_Updater_Encryption($this);

        // Initialize cron manager
        $this->cron_manager = new Q_Updater_Cron_Manager($this);

        // Initialize token sync components
        $this->token_sync = new Q_Updater_Token_Sync($this);
        $this->token_sync_client = new Q_Updater_Token_Sync_Client($this);
        $this->token_sync_ajax = new Q_Updater_Token_Sync_AJAX($this);



        // Initialize migration last (after all other components)
        $this->migration = new Q_Updater_Migration($this);

        // Add actions and filters
        add_action('init', [$this, 'init']);

        // Schedule update check
        $this->schedule_update_check();

        // Clean up any orphaned cron events
        add_action('admin_init', [$this->cron_manager, 'cleanup_cron_option']);
    }

    /**
     * Initialize plugin after translations are loaded
     *
     * This method is hooked to WordPress 'init' action and performs several important tasks:
     * 1. Loads plugin text domain for translations
     * 2. Adds custom cron schedules
     * 3. Sets up admin hooks and filters
     * 4. Registers AJAX handlers
     * 5. Sets up background update hooks
     *
     * Note: This method is called after WordPress core is initialized, ensuring
     * that all WordPress functions are available and translations can be loaded.
     *
     * @since 1.0.0
     */
    public function init()
    {
        // Translations are now loaded in the main plugin file at the init hook
        // to avoid "loaded too early" warnings

        // Add cron schedules filter
        add_filter('cron_schedules', [$this, 'add_cron_schedules']);

        // Setup admin hooks after translations are loaded
        add_action('admin_menu', [$this->settings, 'add_settings_page']);
        add_action('admin_init', [$this->settings, 'register_settings']);
        add_filter('pre_set_site_transient_update_plugins', [$this->updates, 'check_for_update']);
        add_filter('plugins_api', [$this->updates, 'plugin_info'], 10, 3);
        add_filter('auto_update_plugin', [$this->updates, 'auto_update_plugin'], 10, 2);
        add_filter('plugin_auto_update_setting_html', [$this->updates, 'auto_update_setting_html'], 10, 2);
        add_action('admin_notices', [$this->admin, 'show_update_notices']);
        add_action('admin_enqueue_scripts', [$this->admin, 'enqueue_scripts']);

        // Setup AJAX handlers after init
        add_action('wp_ajax_bulk_update_q_plugins', [$this->updates, 'bulk_update_q_plugins']);
        add_action('wp_ajax_manual_check_q_updates', [$this->updates, 'manual_check_q_updates']);
        add_action('wp_ajax_get_plugin_releases', [$this->github_api, 'get_plugin_releases']);
        add_action('wp_ajax_rollback_plugin', [$this->updates, 'rollback_plugin']);
        add_action('wp_ajax_install_github_plugin', [$this->plugin_manager, 'install_github_plugin']);
        add_action('wp_ajax_update_single_plugin', [$this->updates, 'update_single_plugin']);
        add_action('wp_ajax_uninstall_plugin', [$this->updates, 'uninstall_plugin']);
        add_action('wp_ajax_run_auto_updates', [$this->updates, 'run_auto_updates']);
        add_action('wp_ajax_search_github_plugins', [$this->github_api, 'ajax_search_plugins']);
        add_action('wp_ajax_get_plugin_backups', [$this->updates, 'get_plugin_backups']);
        add_action('wp_ajax_restore_plugin_from_backup', [$this->updates, 'restore_plugin_from_backup']);
        add_action('wp_ajax_install_plugin_dependencies', [$this->plugin_manager, 'ajax_install_dependencies']);

        // Analytics AJAX handlers
        add_action('wp_ajax_qu_get_analytics_data', [$this->analytics, 'ajax_get_analytics_data']);
        add_action('wp_ajax_qu_export_analytics_data', [$this->analytics, 'ajax_export_analytics_data']);

        // Tab loading AJAX handler
        add_action('wp_ajax_qu_load_tab_content', [$this->settings, 'ajax_load_tab_content']);





        // Setup background update hooks
        add_action('upgrader_process_complete', [$this->updates, 'store_version_history'], 10, 2);
        add_action('q_updater_background_update', [$this->updates, 'check_for_update']);
        add_action('q_updater_auto_update', [$this->auto_updater, 'process_auto_updates']);
    }

    /**
     * Get option name
     *
     * Retrieves the full option name for a given option key. This centralizes
     * option name management and prevents hardcoding option names throughout the plugin.
     *
     * @since 1.0.0
     * @param string $option Option key (e.g., 'github_token', 'auto_update')
     * @return string Full option name as stored in the database
     */
    public function get_option_name($option)
    {
        $options = [
            'log' => $this->log_option,
            'auto_update' => $this->auto_update_option,
            'email_notification' => $this->email_notification_option,
            'releases_transient_prefix' => $this->releases_transient_prefix,
            'github_token' => $this->github_token_option,
            'github_install_nonce' => $this->github_install_nonce,
            'version_history' => $this->version_history_option,
            'update_frequency' => $this->update_frequency_option,
            'dashboard_notification' => $this->dashboard_notification_option,
            'custom_repo_mappings' => $this->custom_repo_mappings_option,
            'backups' => $this->backups_option,
            'developer_email' => $this->developer_email_option,
            'analytics' => $this->analytics_option,

            'token_sync_is_master' => $this->token_sync_is_master_option,
            'token_sync_master_url' => $this->token_sync_master_url_option,
            'token_sync_key' => $this->token_sync_key_option,
            'token_sync_connected_sites' => $this->token_sync_connected_sites_option,
        ];

        return isset($options[$option]) ? $options[$option] : '';
    }

    /**
     * Get security instance
     *
     * @return Q_Updater_Security Security instance
     */
    public function get_security()
    {
        return $this->security;
    }

    /**
     * Get reviews instance
     *
     * @return Q_Updater_Reviews Reviews instance
     */
    public function get_reviews()
    {
        return $this->reviews;
    }

    /**
     * Get analytics instance
     *
     * @return Q_Updater_Analytics Analytics instance
     */
    public function get_analytics()
    {
        return $this->analytics;
    }

    /**
     * Get token manager instance
     *
     * @return Q_Updater_Token_Manager Token manager instance
     */
    public function get_token_manager()
    {
        return $this->token_manager;
    }

    /**
     * Get CSRF protection instance
     *
     * @return Q_Updater_CSRF_Protection CSRF protection instance
     */
    public function get_csrf_protection()
    {
        return $this->csrf_protection;
    }

    /**
     * Get encryption instance
     *
     * @return Q_Updater_Encryption Encryption instance
     */
    public function get_encryption()
    {
        return $this->encryption;
    }

    /**
     * Get migration instance
     *
     * @return Q_Updater_Migration Migration instance
     */
    public function get_migration()
    {
        return $this->migration;
    }



    /**
     * Get GitHub API instance
     *
     * @return Q_Updater_GitHub_API GitHub API instance
     */
    public function get_github_api()
    {
        return $this->github_api;
    }

    /**
     * Add custom cron schedules
     *
     * Adds weekly and monthly intervals to WordPress cron schedules.
     * This allows the plugin to schedule update checks at these intervals.
     *
     * @since 1.0.0
     * @param array $schedules Existing WordPress cron schedules
     * @return array Modified cron schedules with added intervals
     */
    public function add_cron_schedules($schedules)
    {
        $schedules['weekly'] = [
            'interval' => 7 * DAY_IN_SECONDS,
            'display' => __('Once Weekly', 'q-updater')
        ];

        $schedules['monthly'] = [
            'interval' => 30 * DAY_IN_SECONDS,
            'display' => __('Once Monthly', 'q-updater')
        ];

        return $schedules;
    }

    /**
     * Schedule or reschedule the update check based on frequency setting
     *
     * Sets up the WordPress cron schedule for plugin update checks based on
     * the user's selected frequency. If the frequency is set to 'manual',
     * any existing scheduled events are cleared.
     *
     * This method is called during plugin initialization and whenever
     * the update frequency setting is changed.
     *
     * @since 1.0.0
     */
    public function schedule_update_check()
    {
        $frequency = get_option($this->get_option_name('update_frequency'), 'daily');

        // Use the cron manager to handle scheduling
        if ($frequency !== 'manual') {
            $this->cron_manager->reschedule_event('q_updater_background_update', $frequency);
        } else {
            // If manual, just clear any existing schedule
            $this->cron_manager->clear_scheduled_event('q_updater_background_update');
        }

        // Schedule auto-updates
        $this->auto_updater->schedule_auto_update();
    }

    /**
     * Get cron manager instance
     *
     * @return Q_Updater_Cron_Manager Cron manager instance
     */
    public function get_cron_manager()
    {
        return $this->cron_manager;
    }

    /**
     * Get token sync instance
     *
     * @return Q_Updater_Token_Sync Token sync instance
     */
    public function get_token_sync()
    {
        return $this->token_sync;
    }

    /**
     * Get token sync client instance
     *
     * @return Q_Updater_Token_Sync_Client Token sync client instance
     */
    public function get_token_sync_client()
    {
        return $this->token_sync_client;
    }

    /**
     * Get token sync AJAX instance
     *
     * @return Q_Updater_Token_Sync_AJAX Token sync AJAX instance
     */
    public function get_token_sync_ajax()
    {
        return $this->token_sync_ajax;
    }



    /**
     * Get plugin manager instance
     *
     * @since 1.3.0
     * @return Q_Updater_Plugin_Manager Plugin manager instance
     */
    public function get_plugin_manager()
    {
        return $this->plugin_manager;
    }
}
